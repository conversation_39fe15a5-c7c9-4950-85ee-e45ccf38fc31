FROM nginx:alpine

# Installer OpenSSL for sertifikatgenerering
RUN apk add --no-cache openssl

# Kopier Nginx-konfigurasjon
COPY nginx.conf /etc/nginx/nginx.conf

# Kopier SSL-genereringsskript
COPY generate-ssl.sh /usr/local/bin/generate-ssl.sh
RUN chmod +x /usr/local/bin/generate-ssl.sh

# Opprett SSL-mappe
RUN mkdir -p /etc/nginx/ssl

# Eksponer port 443 for HTTPS
EXPOSE 443

# Generer SSL-sertifikater og start Nginx
CMD ["/bin/sh", "-c", "/usr/local/bin/generate-ssl.sh && nginx -g 'daemon off;'"]
