FROM nginx:alpine

# Installer OpenSSL for fallback sertifikater
RUN apk add --no-cache openssl

# Kopier produksjons Nginx-konfigurasjon
COPY nginx.production.conf /etc/nginx/nginx.conf

# Kopier startup script
COPY start-nginx.sh /usr/local/bin/start-nginx.sh
RUN chmod +x /usr/local/bin/start-nginx.sh

# Opprett nødvendige mapper
RUN mkdir -p /etc/nginx/ssl /var/www/html

# Eksponer porter
EXPOSE 80 443

# Start script som håndterer sertifikater
CMD ["/usr/local/bin/start-nginx.sh"]
