#!/bin/sh

echo "🚀 Starter Nginx med Let's Encrypt støtte..."

# Erstatt miljøvariabler i nginx.conf
envsubst '${DOMAIN}' < /etc/nginx/nginx.conf > /tmp/nginx.conf
mv /tmp/nginx.conf /etc/nginx/nginx.conf

# Sjekk om Let's Encrypt sertifikater eksisterer
if [ -f "/etc/letsencrypt/live/${DOMAIN}/fullchain.pem" ]; then
    echo "✅ Let's Encrypt sertifikater funnet for ${DOMAIN}"
    echo "🔒 Starter Nginx med HTTPS..."
    nginx -g 'daemon off;'
else
    echo "⚠️  Ingen Let's Encrypt sertifikater funnet for ${DOMAIN}"
    echo "🔧 Starter Nginx i HTTP-modus for sertifikatvalidering..."
    
    # Midlertidig nginx.conf for kun HTTP (for Let's Encrypt validering)
    cat > /etc/nginx/nginx.conf << 'EOF'
events {
    worker_connections 1024;
}

http {
    server {
        listen 80;
        server_name _;
        
        location /.well-known/acme-challenge/ {
            root /var/www/html;
        }
        
        location / {
            return 200 "Waiting for SSL certificate...";
            add_header Content-Type text/plain;
        }
    }
}
EOF
    
    echo "📋 For å få SSL-sertifikat, kjør:"
    echo "   docker-compose exec certbot certbot certonly --webroot --webroot-path=/var/www/html --email ${EMAIL} --agree-tos --no-eff-email -d ${DOMAIN}"
    echo "   docker restart product-ikt-nginx"
    
    nginx -g 'daemon off;'
fi
