# PowerShell script for å teste HTTPS-oppsettet

Write-Host "🔍 Tester HTTPS-oppsett..." -ForegroundColor Cyan

# Test HTTP redirect til HTTPS
Write-Host "`n📡 Tester HTTP redirect..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost" -MaximumRedirection 0 -ErrorAction SilentlyContinue
    if ($response.StatusCode -eq 301 -or $response.StatusCode -eq 302) {
        Write-Host "✅ HTTP redirecter korrekt til HTTPS" -ForegroundColor Green
    } else {
        Write-Host "❌ HTTP redirect fungerer ikke som forventet" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Kunne ikke teste HTTP redirect: $($_.Exception.Message)" -ForegroundColor Red
}

# Test HTTPS tilkobling (ignorerer SSL-sertifikatfeil)
Write-Host "`n🔒 Tester HTTPS tilkobling..." -ForegroundColor Yellow
try {
    # Ignorer SSL-sertifikatfeil for self-signed sertifikater
    [System.Net.ServicePointManager]::ServerCertificateValidationCallback = {$true}
    
    $response = Invoke-WebRequest -Uri "https://localhost" -UseBasicParsing
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ HTTPS tilkobling fungerer!" -ForegroundColor Green
        Write-Host "   Status: $($response.StatusCode)" -ForegroundColor Gray
    } else {
        Write-Host "❌ HTTPS tilkobling feilet med status: $($response.StatusCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ HTTPS tilkobling feilet: $($_.Exception.Message)" -ForegroundColor Red
}

# Test API endpoint
Write-Host "`n🔌 Tester API endpoint..." -ForegroundColor Yellow
try {
    $apiResponse = Invoke-WebRequest -Uri "https://localhost/api/products" -UseBasicParsing
    if ($apiResponse.StatusCode -eq 200) {
        Write-Host "✅ API endpoint fungerer!" -ForegroundColor Green
        $products = $apiResponse.Content | ConvertFrom-Json
        Write-Host "   Antall produkter: $($products.Count)" -ForegroundColor Gray
    } else {
        Write-Host "❌ API endpoint feilet med status: $($apiResponse.StatusCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ API endpoint feilet: $($_.Exception.Message)" -ForegroundColor Red
}

# Test health check
Write-Host "`n❤️ Tester health check..." -ForegroundColor Yellow
try {
    $healthResponse = Invoke-WebRequest -Uri "https://localhost/health" -UseBasicParsing
    if ($healthResponse.StatusCode -eq 200) {
        Write-Host "✅ Health check OK!" -ForegroundColor Green
        Write-Host "   Response: $($healthResponse.Content.Trim())" -ForegroundColor Gray
    } else {
        Write-Host "❌ Health check feilet" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Health check feilet: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🌐 Åpne i nettleser:" -ForegroundColor Cyan
Write-Host "   HTTPS: https://localhost" -ForegroundColor White
Write-Host "   API:   https://localhost/api/products" -ForegroundColor White

Write-Host "`n📋 Docker kommandoer:" -ForegroundColor Cyan
Write-Host "   Logs:    docker logs product-ikt-nginx" -ForegroundColor White
Write-Host "   Restart: docker restart product-ikt-nginx" -ForegroundColor White
Write-Host "   Test:    docker exec product-ikt-nginx nginx -t" -ForegroundColor White
