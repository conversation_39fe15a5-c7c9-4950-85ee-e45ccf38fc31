version: '3.8'

services:
  app:                              # Applikasjons-tjeneste (Flask)
    container_name: product-ikt-app     # Navn på containeren for Flask-appen
    build:                          # Bygger et image fra en Dockerfile
      context: ./FlaskProject       # Mappen der Flask-kode og Dockerfile ligger
      dockerfile: Dockerfile        # Navnet på Dockerfile som brukes ved bygging
    environment:                    # Miljøvariabler for Flask-appen
      FLASK_APP: app.py             # Angir hvilken fil som kjører Flask
    ports:                          # Port-eksponering slik at vert kan nå app
      - "5000:5000"                 # 5000 på vert kobles til 5000 i container
    depends_on:                     # Avhengighetshåndtering
      - db                          # App starter først etter at db er klar
    networks:                       # Angir hvilket nettverk denne tjenesten bruker
      - product-db-ikt-con          # Knytter tjenesten til det definerte nettverket

  db:                               # Database-tjeneste (MySQL)
    container_name: product-ikt-db      # Navn på containeren for enkel identifikasjon
    build:                          # Bygger et image fra en Dockerfile
      context: ./db                 # Mappen der Dockerfile og init-skript ligger
      dockerfile: Dockerfile        # Navnet på Dockerfile som brukes ved bygging
    environment:                    # Miljøvariabler for MySQL-konfigurasjon
      MYSQL_ROOT_PASSWORD: gokstad  # Setter root-passordet
    ports:                          # Port-viderekobling fra vert til container
      - "3306:3306"                 # 3306 på vert kobles til 3306 i container
    volumes:                        # Volum-mount for vedvarende database-lagring
      - db-data:/var/lib/mysql      # db-data-volum mountes til MySQL datakatalog
    networks:                       # Angir hvilket nettverk denne tjenesten bruker
      - product-db-ikt-con          # Knytter tjenesten til det definerte nettverket

volumes:                            # Definerer eksterne volumer for tjenester
  db-data:                          # Volum med navn db-data for MySQL

networks:                           # Definerer nettverk for kommunikasjon
  product-db-ikt-con:               # Navgitt nettverk mellom tjenester
    driver: bridge                  # Bruker standard bridge-nettverk for isolasjon
