# Nginx HTTPS Oppsett

Dette oppsettet konfigurerer Nginx som en reverse proxy foran Flask-applikasjonen din med HTTPS-støtte på port 443.

## Hva som er satt opp

### 🔒 SSL/HTTPS
- **Port 443**: HTTPS med SSL-sertifikater
- **Port 80**: HTTP som automatisk redirecter til HTTPS
- **Self-signed sertifikater**: Automatisk generert for utvikling
- **Sikkerhetshoder**: HSTS, X-Frame-Options, etc.

### 🔄 Reverse Proxy
- Nginx videresender alle forespørsler til Flask-appen
- Håndterer CORS for API-endepunkter
- Optimalisert med gzip-komprimering

### 📁 Filer
- `Dockerfile`: Nginx container med SSL-støtte
- `nginx.conf`: Nginx konfigurasjon med HTTPS
- `generate-ssl.sh`: Script for å generere SSL-sertifikater

## H<PERSON><PERSON> bruke

### 1. Start tjenestene
```bash
docker-compose up --build
```

### 2. Tilgang til applikasjonen
- **HTTPS**: https://localhost (port 443)
- **HTTP**: http://localhost (redirecter til HTTPS)

### 3. Første gang - Sertifikatadvarsel
Siden vi bruker self-signed sertifikater, vil nettleseren vise en sikkerhetsadvarsel første gang:

**Chrome/Edge:**
1. Klikk "Advanced" / "Avansert"
2. Klikk "Proceed to localhost (unsafe)" / "Fortsett til localhost (utrygt)"

**Firefox:**
1. Klikk "Advanced" / "Avansert"
2. Klikk "Accept the Risk and Continue" / "Godta risikoen og fortsett"

## Produksjonsoppsett med Let's Encrypt

For produksjon bør du bruke ekte SSL-sertifikater fra Let's Encrypt:

### 1. Installer Certbot
```bash
# På Ubuntu/Debian
sudo apt install certbot python3-certbot-nginx

# På CentOS/RHEL
sudo yum install certbot python3-certbot-nginx
```

### 2. Få SSL-sertifikat
```bash
sudo certbot --nginx -d dittdomene.no
```

### 3. Oppdater docker-compose.yml
```yaml
nginx:
  volumes:
    - /etc/letsencrypt:/etc/letsencrypt:ro
    - ssl-certs:/etc/nginx/ssl
```

## Feilsøking

### Nginx starter ikke
```bash
# Sjekk Nginx-logger
docker logs product-ikt-nginx

# Test Nginx-konfigurasjon
docker exec product-ikt-nginx nginx -t
```

### SSL-problemer
```bash
# Regenerer sertifikater
docker exec product-ikt-nginx /usr/local/bin/generate-ssl.sh

# Restart Nginx
docker restart product-ikt-nginx
```

### Kan ikke nå Flask-appen
```bash
# Sjekk at Flask kjører
docker logs product-ikt-app

# Test direkte tilkobling til Flask (kun for testing)
curl http://localhost:5000/api/products
```

## Sikkerhet

### Produksjonsanbefalinger
1. **Bruk ekte SSL-sertifikater** (Let's Encrypt)
2. **Oppdater server_name** i nginx.conf til ditt domene
3. **Konfigurer firewall** til kun å tillate port 443 og 80
4. **Aktiver fail2ban** for å beskytte mot brute force
5. **Regelmessige sikkerhetsoppdateringer**

### Self-signed sertifikater
- ⚠️ **Kun for utvikling** - ikke bruk i produksjon
- Gyldig i 365 dager
- Automatisk regenerert hvis slettet
