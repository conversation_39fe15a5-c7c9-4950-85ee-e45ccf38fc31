#!/bin/sh

# SSL-sertifikat konfigurasjon
SSL_DIR="/etc/nginx/ssl"
CERT_FILE="$SSL_DIR/nginx.crt"
KEY_FILE="$SSL_DIR/nginx.key"

# Sjekk om sertifikater allerede eksisterer
if [ -f "$CERT_FILE" ] && [ -f "$KEY_FILE" ]; then
    echo "SSL-sertifikater eksisterer allerede, hopper over generering..."
else
    echo "Genererer self-signed SSL-sertifikater..."
    
    # Opprett SSL-mappe hvis den ikke eksisterer
    mkdir -p "$SSL_DIR"
    
    # Generer private key
    openssl genrsa -out "$KEY_FILE" 2048
    
    # Generer self-signed sertifikat
    openssl req -new -x509 -key "$KEY_FILE" -out "$CERT_FILE" -days 365 -subj "/C=NO/ST=Vestfold/L=Horten/O=IKT/OU=Development/CN=localhost"
    
    # Sett riktige tillatelser
    chmod 600 "$KEY_FILE"
    chmod 644 "$CERT_FILE"
    
    echo "SSL-sertifikater generert:"
    echo "  Sertifikat: $CERT_FILE"
    echo "  Private key: $KEY_FILE"
    echo "  Gyldig i 365 dager"
fi

echo "SSL-oppsett fullført!"
