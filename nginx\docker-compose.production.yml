# Produksjonsversjon med Let's Encrypt Certbot
version: '3.8'

services:
  nginx:
    container_name: product-ikt-nginx
    build:
      context: ./nginx
      dockerfile: Dockerfile.production
    ports:
      - "443:443"
      - "80:80"
    depends_on:
      - app
    volumes:
      - certbot-etc:/etc/letsencrypt
      - certbot-var:/var/lib/letsencrypt
      - web-root:/var/www/html
    networks:
      - product-db-ikt-con
    environment:
      - DOMAIN=dittdomene.no  # Endre til ditt domene
      - EMAIL=<EMAIL>    # Endre til din e-post

  certbot:
    image: certbot/certbot
    container_name: product-ikt-certbot
    volumes:
      - certbot-etc:/etc/letsencrypt
      - certbot-var:/var/lib/letsencrypt
      - web-root:/var/www/html
    depends_on:
      - nginx
    command: certonly --webroot --webroot-path=/var/www/html --email <EMAIL> --agree-tos --no-eff-email --staging -d dittdomene.no

  app:
    container_name: product-ikt-app
    build:
      context: ./FlaskProject
      dockerfile: Dockerfile
    environment:
      FLASK_APP: app.py
    networks:
      - product-db-ikt-con

  db:
    container_name: product-ikt-db
    build:
      context: ./db
      dockerfile: Dockerfile
    environment:
      MYSQL_ROOT_PASSWORD: gokstad
    ports:
      - "3306:3306"
    volumes:
      - db-data:/var/lib/mysql
    networks:
      - product-db-ikt-con

volumes:
  db-data:
  certbot-etc:      # Let's Encrypt sertifikater
  certbot-var:      # Let's Encrypt data
  web-root:         # Webroot for domain validering

networks:
  product-db-ikt-con:
    driver: bridge
