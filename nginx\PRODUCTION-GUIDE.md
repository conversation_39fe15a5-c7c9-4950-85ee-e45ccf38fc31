# 🚀 Produksjonsoppsett med Let's Encrypt

Denne guiden viser hvordan du setter opp **ekte SSL-sertifikater** med Let's Encrypt, helt isolert i Docker-containere.

## 📋 Forutsetninger

1. **Eget domene** (f.eks. `mittprosjekt.no`)
2. **Domenet peker til din server** (DNS A-record)
3. **Port 80 og 443 åpne** i brannmur

## 🔧 Oppsett steg-for-steg

### 1. Rediger konfigurasjon
Åpne `nginx/docker-compose.production.yml` og endre:
```yaml
environment:
  - DOMAIN=dittdomene.no    # ← Endre til ditt domene
  - EMAIL=<EMAIL>      # ← Endre til din e-post
```

Endre også i `certbot` seksjonen:
```yaml
command: certonly --webroot --webroot-path=/var/www/html --email <EMAIL> --agree-tos --no-eff-email --staging -d dittdomene.no
```

### 2. Start i staging-modus (testing)
```bash
# Fra hovedmappen
docker-compose -f nginx/docker-compose.production.yml up -d
```

### 3. Få SSL-sertifikat (staging)
```bash
# Test at alt fungerer med staging-sertifikat
docker-compose -f nginx/docker-compose.production.yml exec certbot \
  certbot certonly --webroot --webroot-path=/var/www/html \
  --email <EMAIL> --agree-tos --no-eff-email --staging \
  -d dittdomene.no
```

### 4. Restart Nginx
```bash
docker restart product-ikt-nginx
```

### 5. Test at staging fungerer
Gå til `https://dittdomene.no` - du skal se en advarsel om "staging certificate"

### 6. Få ekte sertifikat
```bash
# Stopp staging
docker-compose -f nginx/docker-compose.production.yml down

# Fjern --staging fra docker-compose.production.yml
# Endre denne linjen:
# command: certonly --webroot --webroot-path=/var/www/html --email <EMAIL> --agree-tos --no-eff-email -d dittdomene.no

# Start på nytt
docker-compose -f nginx/docker-compose.production.yml up -d

# Få ekte sertifikat
docker-compose -f nginx/docker-compose.production.yml exec certbot \
  certbot certonly --webroot --webroot-path=/var/www/html \
  --email <EMAIL> --agree-tos --no-eff-email \
  -d dittdomene.no

# Restart Nginx
docker restart product-ikt-nginx
```

## 🔄 Automatisk fornyelse

Let's Encrypt sertifikater utløper etter 90 dager. Sett opp automatisk fornyelse:

### Opprett fornyelsesscript
```bash
# Opprett script
cat > renew-ssl.sh << 'EOF'
#!/bin/bash
docker-compose -f nginx/docker-compose.production.yml exec certbot certbot renew --quiet
docker restart product-ikt-nginx
EOF

chmod +x renew-ssl.sh
```

### Legg til i crontab (kjører hver dag kl 02:00)
```bash
crontab -e
# Legg til denne linjen:
0 2 * * * /path/to/your/project/renew-ssl.sh
```

## 🛡️ Sikkerhet

### Fordeler med Docker Certbot:
- ✅ **Isolert**: Certbot kjører i egen container
- ✅ **Ingen systemendringer**: Påvirker ikke din server
- ✅ **Lett å fjerne**: `docker-compose down` fjerner alt
- ✅ **Versjonskontroll**: Samme oppsett overalt

### Vs. systeminstallasjon:
- ❌ Systeminstallasjon krever root-tilgang
- ❌ Kan påvirke andre tjenester
- ❌ Vanskeligere å feilsøke
- ❌ Forskjellig på ulike operativsystemer

## 🔍 Feilsøking

### Sjekk Certbot-logger
```bash
docker logs product-ikt-certbot
```

### Test sertifikat
```bash
# Sjekk sertifikatinfo
openssl s_client -connect dittdomene.no:443 -servername dittdomene.no

# Test SSL-rating
curl -I https://dittdomene.no
```

### Vanlige problemer

**Problem**: "Domain validation failed"
**Løsning**: Sjekk at domenet peker til riktig IP og port 80 er åpen

**Problem**: "Rate limit exceeded"
**Løsning**: Bruk `--staging` først for testing

**Problem**: "Certificate not found"
**Løsning**: Sjekk at sertifikatfiler eksisterer i `/etc/letsencrypt/live/`

## 📊 Sammenligning

| Metode | Sikkerhet | Kompleksitet | Vedlikehold |
|--------|-----------|--------------|-------------|
| **Docker Certbot** | ✅ Høy | 🟡 Middels | ✅ Enkelt |
| Systeminstallasjon | 🟡 Middels | 🔴 Høy | 🔴 Komplisert |
| Self-signed | ✅ Høy | ✅ Lav | ✅ Ingen |

**Anbefaling**: Bruk Docker Certbot for produksjon! 🎯
